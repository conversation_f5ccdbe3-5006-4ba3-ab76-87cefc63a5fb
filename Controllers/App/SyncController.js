const { Op, Transaction } = require('sequelize');
const {
  BUTTON_TYPES,
  OPERATION_TYPES,
  BUTTON_TYPES_NUMBERS,
  TAG_TYPES,
} = require('../../Configs/constants');
const { API } = require('../../Configs/message');

const ButtonModel = new (require('../../Models/App/ButtonModel'))();
const ItemModel = new (require('../../Models/App/ItemModel'))();
const TagModel = new (require('../../Models/App/TagModel'))();
const AlarmModel = new (require('../../Models/App/AlarmModel'))();

// Enhanced logging utility for structured logging
class SyncLogger {
  static logOperation(operation, stage, details = {}) {
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      stage,
      operationType: operation.operationType,
      syncId: operation.syncId,
      originalIndex: operation.originalIndex,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      localTagId: operation.localTagId,
      buttonUuid: operation.buttonUuid,
      itemUuid: operation.itemUuid,
      tagUuid: operation.tagUuid,
      // payload: operation.payload,
      ...details,
    };

    console.log(
      `[SyncController] [${stage}] Operation ${operation.originalIndex}:`,
      JSON.stringify(logData, null, 2)
    );
  }

  static logPerformance(metrics, additionalData = {}) {
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      type: 'PERFORMANCE_METRICS',
      ...metrics,
      ...additionalData,
    };

    console.log(
      `[SyncController] [PERFORMANCE]`,
      JSON.stringify(logData, null, 2)
    );
  }

  static logBulkOperation(operationType, operations, stage, details = {}) {
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      type: 'BULK_OPERATION',
      stage,
      operationType,
      operationCount: operations.length,
      operations: operations.map((op) => ({
        syncId: op.syncId,
        originalIndex: op.originalIndex,
        localButtonId: op.localButtonId,
        localItemId: op.localItemId,
        localTagId: op.localTagId,
        buttonUuid: op.buttonUuid,
        itemUuid: op.itemUuid,
        tagUuid: op.tagUuid,
        // payload: op.payload
      })),
      ...details,
    };

    console.log(
      `[SyncController] [BULK_${stage}]`,
      JSON.stringify(logData, null, 2)
    );
  }

  static logError(operation, error, stage, details = {}) {
    const timestamp = new Date().toISOString();
    const logData = {
      timestamp,
      type: 'ERROR',
      stage,
      operationType: operation?.operationType,
      syncId: operation?.syncId,
      originalIndex: operation?.originalIndex,
      error: {
        message: error.message,
        code: error.code,
        stack: error.stack,
      },
      ...details,
    };

    console.error(
      `[SyncController] [ERROR] [${stage}]`,
      JSON.stringify(logData, null, 2)
    );
  }
}

class SyncController {
  #buttonIdMap = new Map();
  #itemIdMap = new Map();
  #tagIdMap = new Map();
  #alarmIdMap = new Map();

  // Cache for database lookups to reduce N+1 queries (using UUIDs as keys)
  #buttonCache = new Map(); // key: button_uuid, value: button object
  #itemCache = new Map(); // key: item_uuid, value: item object
  #tagCache = new Map(); // key: tag_uuid, value: tag object
  #alarmCache = new Map(); // key: button_uuid, value: alarm object

  // Enhanced performance tracking
  #performanceMetrics = {
    totalOperations: 0,
    operationsByType: new Map(),
    startTime: null,
    endTime: null,
    dbQueries: {
      total: 0,
      byOperation: new Map(), // Track queries per operation type
      byStage: new Map(), // Track queries per processing stage
      details: [], // Detailed query log with timestamps
    },
    bulkOperations: {
      total: 0,
      byType: new Map(), // Track bulk operations by type
      details: [], // Detailed bulk operation log
    },
    cacheHits: {
      total: 0,
      byType: new Map(), // Track cache hits by entity type
      details: [], // Detailed cache hit log
    },
    parallelOperations: 0,
    processingStages: {
      grouping: { startTime: null, endTime: null, duration: 0 },
      buttons: { startTime: null, endTime: null, duration: 0 },
      items: { startTime: null, endTime: null, duration: 0 },
      tags: { startTime: null, endTime: null, duration: 0 },
      alarms: { startTime: null, endTime: null, duration: 0 },
      sequences: { startTime: null, endTime: null, duration: 0 },
    },
    errors: [],
    warnings: [],
  };

  constructor() {
    this.batchSync = this.batchSync.bind(this);
  }

  #resetMaps() {
    this.#buttonIdMap.clear();
    this.#itemIdMap.clear();
    this.#tagIdMap.clear();
    this.#alarmIdMap.clear();

    // Clear caches
    this.#buttonCache.clear();
    this.#itemCache.clear();
    this.#tagCache.clear();
    this.#alarmCache.clear();

    // Reset enhanced performance metrics
    this.#performanceMetrics = {
      totalOperations: 0,
      operationsByType: new Map(),
      startTime: null,
      endTime: null,
      dbQueries: {
        total: 0,
        byOperation: new Map(),
        byStage: new Map(),
        details: [],
      },
      bulkOperations: {
        total: 0,
        byType: new Map(),
        details: [],
      },
      cacheHits: {
        total: 0,
        byType: new Map(),
        details: [],
      },
      parallelOperations: 0,
      processingStages: {
        grouping: { startTime: null, endTime: null, duration: 0 },
        buttons: { startTime: null, endTime: null, duration: 0 },
        items: { startTime: null, endTime: null, duration: 0 },
        tags: { startTime: null, endTime: null, duration: 0 },
        alarms: { startTime: null, endTime: null, duration: 0 },
        sequences: { startTime: null, endTime: null, duration: 0 },
      },
      errors: [],
      warnings: [],
    };
  }

  // Helper methods for performance tracking
  #trackDbQuery(operationType, stage, queryDetails = {}) {
    const timestamp = Date.now();
    this.#performanceMetrics.dbQueries.total++;

    // Track by operation type
    const opCount =
      this.#performanceMetrics.dbQueries.byOperation.get(operationType) || 0;
    this.#performanceMetrics.dbQueries.byOperation.set(
      operationType,
      opCount + 1
    );

    // Track by stage
    const stageCount =
      this.#performanceMetrics.dbQueries.byStage.get(stage) || 0;
    this.#performanceMetrics.dbQueries.byStage.set(stage, stageCount + 1);

    // Add detailed log
    this.#performanceMetrics.dbQueries.details.push({
      timestamp,
      operationType,
      stage,
      ...queryDetails,
    });
  }

  #trackBulkOperation(operationType, count, details = {}) {
    const timestamp = Date.now();
    this.#performanceMetrics.bulkOperations.total++;

    const typeCount =
      this.#performanceMetrics.bulkOperations.byType.get(operationType) || 0;
    this.#performanceMetrics.bulkOperations.byType.set(
      operationType,
      typeCount + 1
    );

    this.#performanceMetrics.bulkOperations.details.push({
      timestamp,
      operationType,
      count,
      ...details,
    });
  }

  #trackCacheHit(entityType, details = {}) {
    const timestamp = Date.now();
    this.#performanceMetrics.cacheHits.total++;

    const typeCount =
      this.#performanceMetrics.cacheHits.byType.get(entityType) || 0;
    this.#performanceMetrics.cacheHits.byType.set(entityType, typeCount + 1);

    this.#performanceMetrics.cacheHits.details.push({
      timestamp,
      entityType,
      ...details,
    });
  }

  #startStage(stageName) {
    if (this.#performanceMetrics.processingStages[stageName]) {
      this.#performanceMetrics.processingStages[stageName].startTime =
        Date.now();
    }
  }

  #endStage(stageName) {
    if (
      this.#performanceMetrics.processingStages[stageName] &&
      this.#performanceMetrics.processingStages[stageName].startTime
    ) {
      const endTime = Date.now();
      this.#performanceMetrics.processingStages[stageName].endTime = endTime;
      this.#performanceMetrics.processingStages[stageName].duration =
        endTime -
        this.#performanceMetrics.processingStages[stageName].startTime;
    }
  }

  // Helper method to safely convert Map to Object
  #mapToObject(map) {
    if (!map || typeof map.entries !== 'function') {
      return {};
    }
    try {
      return Object.fromEntries(map);
    } catch (error) {
      console.warn('[SyncController] Failed to convert Map to Object:', error);
      return {};
    }
  }

  // Helper method to check if request is aborted
  #checkRequestAborted(req) {
    if (req?.aborted || req.socket?.destroyed) {
      const error = new Error('Request was aborted by client');
      error.code = 'REQUEST_ABORTED';
      error.message = 'Request was aborted by client';
      throw error;
    }
  }

  // Create AbortController for internal operations
  #createAbortController(req) {
    const controller = new AbortController();

    // Listen for client disconnect
    const onAbort = () => {
      console.log('Client disconnected, aborting operations...');
      controller.abort();
    };

    req.on('aborted', onAbort);
    req.on('close', onAbort);
    req.socket?.on('close', onAbort);

    // Cleanup listeners when done
    const cleanup = () => {
      req.removeListener('aborted', onAbort);
      req.removeListener('close', onAbort);
      req.socket?.removeListener('close', onAbort);
    };

    return { controller, cleanup };
  }

  // Helper methods for error handling
  #getErrorMessage(error) {
    if (error.parent?.code === 'ER_SERVER_SHUTDOWN') {
      return 'Database server is shutting down. Please try again later.';
    }
    if (error.parent?.code === 'PROTOCOL_CONNECTION_LOST') {
      return 'Database connection lost. Please try again.';
    }
    if (error.code === 'ER_SERVER_SHUTDOWN') {
      return 'Database server is shutting down. Please try again later.';
    }
    if (error.code === 'PROTOCOL_CONNECTION_LOST') {
      return 'Database connection lost. Please try again.';
    }
    return error.message || 'An unexpected error occurred during sync';
  }

  #getErrorCode(error) {
    if (error.parent?.code) {
      return error.parent.code;
    }
    if (error.code) {
      return error.code;
    }
    return 'SYNC_ERROR';
  }

  #isRetryableError(error) {
    const retryableCodes = [
      'ER_SERVER_SHUTDOWN',
      'PROTOCOL_CONNECTION_LOST',
      'ER_LOCK_WAIT_TIMEOUT',
      'ER_LOCK_DEADLOCK',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
    ];

    return (
      retryableCodes.includes(error.code) ||
      retryableCodes.includes(error.parent?.code)
    );
  }

  // Check database connection health
  async #checkDatabaseConnection() {
    try {
      // Use the global sequelize instance
      await global.sequelize.authenticate();
      return true;
    } catch (error) {
      console.error(
        '[SyncController] Database connection check failed:',
        error
      );
      return false;
    }
  }

  // Group operations by type for potential bulk processing with comprehensive logging
  #groupOperationsByType(operations) {
    this.#startStage('grouping');

    const grouped = {
      buttons: { create: [], update: [], delete: [] },
      items: { create: [], update: [], delete: [] },
      tags: { create: [], update: [], delete: [] },
      alarms: { update: [] },
      sequences: { update: [] },
    };

    // Log all operations being processed
    SyncLogger.logBulkOperation(
      'ALL_OPERATIONS',
      operations,
      'GROUPING_START',
      {
        totalOperations: operations.length,
        operationTypes: operations.map((op) => op.operationType),
      }
    );

    operations.forEach((operation, index) => {
      // Log each individual operation with complete payload
      SyncLogger.logOperation(operation, 'GROUPING', {
        index,
        groupingStage: 'PROCESSING',
      });

      // Track operation types for metrics
      const opType = operation.operationType;
      this.#performanceMetrics.operationsByType.set(
        opType,
        (this.#performanceMetrics.operationsByType.get(opType) || 0) + 1
      );

      // Add index for error tracking
      const opWithIndex = { ...operation, originalIndex: index };

      switch (operation.operationType) {
        case OPERATION_TYPES.ADD_BUTTON:
          grouped.buttons.create.push(opWithIndex);
          break;
        case OPERATION_TYPES.EDIT_BUTTON:
          grouped.buttons.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.DELETE_BUTTON:
          grouped.buttons.delete.push(opWithIndex);
          break;
        case OPERATION_TYPES.CREATE_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.CREATE_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.CREATE_VALUE_BUTTON_ITEM:
          grouped.items.create.push(opWithIndex);
          break;
        case OPERATION_TYPES.EDIT_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.EDIT_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.EDIT_VALUE_BUTTON_ITEM:
          grouped.items.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.DELETE_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.DELETE_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.DELETE_VALUE_BUTTON_ITEM:
          grouped.items.delete.push(opWithIndex);
          break;
        case OPERATION_TYPES.CREATE_ITEM_TAG:
          grouped.tags.create.push(opWithIndex);
          break;
        case OPERATION_TYPES.EDIT_ITEM_TAG:
          grouped.tags.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.DELETE_ITEM_TAG:
          grouped.tags.delete.push(opWithIndex);
          break;
        case OPERATION_TYPES.UPDATE_BUTTON_ALARM:
          grouped.alarms.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.CHANGE_BUTTON_SEQUENCE:
          grouped.sequences.update.push(opWithIndex);
          break;
      }
    });

    // Log grouped operations summary
    const groupingSummary = {
      buttons: {
        create: grouped.buttons.create.length,
        update: grouped.buttons.update.length,
        delete: grouped.buttons.delete.length,
      },
      items: {
        create: grouped.items.create.length,
        update: grouped.items.update.length,
        delete: grouped.items.delete.length,
      },
      tags: {
        create: grouped.tags.create.length,
        update: grouped.tags.update.length,
        delete: grouped.tags.delete.length,
      },
      alarms: { update: grouped.alarms.update.length },
      sequences: { update: grouped.sequences.update.length },
    };

    SyncLogger.logBulkOperation(
      'ALL_OPERATIONS',
      operations,
      'GROUPING_COMPLETE',
      {
        groupingSummary,
        operationsByType: this.#mapToObject(
          this.#performanceMetrics.operationsByType
        ),
      }
    );

    this.#endStage('grouping');
    return grouped;
  }

  // Process operations in optimized order with bulk operations where possible
  async #processOperationsOptimized(
    groupedOperations,
    userId,
    transaction,
    abortController,
    req
  ) {
    const responseData = [];

    // Log processing start with complete grouped operations summary
    SyncLogger.logBulkOperation('PROCESSING', [], 'START', {
      userId,
      groupedOperations: {
        buttons: {
          create: groupedOperations.buttons.create.length,
          update: groupedOperations.buttons.update.length,
          delete: groupedOperations.buttons.delete.length,
        },
        items: {
          create: groupedOperations.items.create.length,
          update: groupedOperations.items.update.length,
          delete: groupedOperations.items.delete.length,
        },
        tags: {
          create: groupedOperations.tags.create.length,
          update: groupedOperations.tags.update.length,
          delete: groupedOperations.tags.delete.length,
        },
        alarms: { update: groupedOperations.alarms.update.length },
        sequences: { update: groupedOperations.sequences.update.length },
      },
    });

    // Process in dependency order: Buttons -> Items -> Tags -> Alarms -> Sequences

    // 1. Process Button Operations
    this.#startStage('buttons');
    SyncLogger.logBulkOperation(
      'BUTTONS',
      groupedOperations.buttons.create.concat(
        groupedOperations.buttons.update,
        groupedOperations.buttons.delete
      ),
      'START'
    );

    await this.#processBulkButtonOperations(
      groupedOperations.buttons,
      userId,
      transaction,
      responseData,
      abortController,
      req
    );

    this.#endStage('buttons');
    SyncLogger.logBulkOperation('BUTTONS', [], 'COMPLETE');

    // 2. Process Item Operations and independent operations in parallel
    const parallelPromises = [];

    // Items depend on buttons, so process after buttons
    if (
      groupedOperations.items.create.length > 0 ||
      groupedOperations.items.update.length > 0 ||
      groupedOperations.items.delete.length > 0
    ) {
      this.#startStage('items');
      SyncLogger.logBulkOperation(
        'ITEMS',
        groupedOperations.items.create.concat(
          groupedOperations.items.update,
          groupedOperations.items.delete
        ),
        'START'
      );

      parallelPromises.push(
        this.#processBulkItemOperations(
          groupedOperations.items,
          userId,
          transaction,
          responseData,
          abortController,
          req
        ).then(() => {
          this.#endStage('items');
          SyncLogger.logBulkOperation('ITEMS', [], 'COMPLETE');
        })
      );
    }

    // Alarms and sequences can run in parallel with items (they only depend on buttons)
    if (groupedOperations.alarms.update.length > 0) {
      this.#startStage('alarms');
      SyncLogger.logBulkOperation(
        'ALARMS',
        groupedOperations.alarms.update,
        'START'
      );

      parallelPromises.push(
        this.#processBulkAlarmOperations(
          groupedOperations.alarms,
          userId,
          transaction,
          responseData,
          abortController,
          req
        ).then(() => {
          this.#endStage('alarms');
          SyncLogger.logBulkOperation('ALARMS', [], 'COMPLETE');
        })
      );
    }

    if (groupedOperations.sequences.update.length > 0) {
      this.#startStage('sequences');
      SyncLogger.logBulkOperation(
        'SEQUENCES',
        groupedOperations.sequences.update,
        'START'
      );

      parallelPromises.push(
        this.#processBulkSequenceOperations(
          groupedOperations.sequences,
          userId,
          transaction,
          responseData,
          abortController,
          req
        ).then(() => {
          this.#endStage('sequences');
          SyncLogger.logBulkOperation('SEQUENCES', [], 'COMPLETE');
        })
      );
    }

    // Execute parallel operations
    if (parallelPromises.length > 0) {
      await Promise.all(parallelPromises);
      this.#performanceMetrics.parallelOperations += parallelPromises.length;
    }

    // 3. Process Tag Operations last (depends on items)
    this.#startStage('tags');
    SyncLogger.logBulkOperation(
      'TAGS',
      groupedOperations.tags.create.concat(
        groupedOperations.tags.update,
        groupedOperations.tags.delete
      ),
      'START'
    );

    await this.#processBulkTagOperations(
      groupedOperations.tags,
      userId,
      transaction,
      responseData,
      abortController,
      req
    );

    this.#endStage('tags');
    SyncLogger.logBulkOperation('TAGS', [], 'COMPLETE');

    // Sort response data by original operation order
    responseData.sort((a, b) => a.originalIndex - b.originalIndex);

    // Log processing completion
    SyncLogger.logBulkOperation('PROCESSING', [], 'COMPLETE', {
      totalResponseItems: responseData.length,
      processingStages: this.#performanceMetrics.processingStages,
    });

    return responseData.map((item) => {
      const { originalIndex, ...response } = item;
      return response;
    });
  }

  // Bulk process button operations with comprehensive logging
  async #processBulkButtonOperations(
    buttonOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    // Process creates first (bulk create)
    if (buttonOps.create.length > 0) {
      SyncLogger.logBulkOperation('BUTTON_CREATE', buttonOps.create, 'START', {
        userId,
        operationCount: buttonOps.create.length,
      });

      await this.#bulkCreateButtons(
        buttonOps.create,
        userId,
        transaction,
        responseData,
        abortController,
        req
      );

      SyncLogger.logBulkOperation(
        'BUTTON_CREATE',
        buttonOps.create,
        'COMPLETE'
      );
    }

    // Process updates individually (need to check existing buttons)
    for (const operation of buttonOps.update) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        SyncLogger.logError(
          operation,
          new Error('Request aborted'),
          'BUTTON_UPDATE'
        );
        throw new Error('Request was aborted by client');
      }

      try {
        SyncLogger.logOperation(operation, 'BUTTON_UPDATE_START', { userId });
        await this.updateButtonSync(operation, userId, transaction);
        this.#addButtonResponse(operation, responseData);
        SyncLogger.logOperation(operation, 'BUTTON_UPDATE_SUCCESS', { userId });
      } catch (error) {
        SyncLogger.logError(operation, error, 'BUTTON_UPDATE', { userId });
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }
    }

    // Process deletes individually (need to check existing buttons)
    for (const operation of buttonOps.delete) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        SyncLogger.logError(
          operation,
          new Error('Request aborted'),
          'BUTTON_DELETE'
        );
        throw new Error('Request was aborted by client');
      }

      try {
        SyncLogger.logOperation(operation, 'BUTTON_DELETE_START', { userId });
        await this.deleteButtonSync(operation, userId, transaction);
        this.#addButtonResponse(operation, responseData);
        SyncLogger.logOperation(operation, 'BUTTON_DELETE_SUCCESS', { userId });
      } catch (error) {
        SyncLogger.logError(operation, error, 'BUTTON_DELETE', { userId });
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }
    }
  }

  // Bulk create buttons for better performance with enhanced logging
  async #bulkCreateButtons(
    createOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    if (createOps.length === 0) {
      SyncLogger.logBulkOperation('BUTTON_CREATE', [], 'SKIP', {
        reason: 'No operations',
      });
      return;
    }

    SyncLogger.logBulkOperation(
      'BUTTON_CREATE',
      createOps,
      'PAYLOAD_PREPARATION_START',
      {
        userId,
        operationCount: createOps.length,
      }
    );

    const bulkPayload = [];

    const updateOnDuplicateFields = [
      'button_name',
      'button_color',
      'button_type',
      'button_shape',
      'button_summery_calculation',
      'button_sequence',
      'alarm_tag',
      'text_note_tag',
      'location_tag',
    ];

    for (const operation of createOps) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        SyncLogger.logError(
          operation,
          new Error('Request aborted'),
          'BUTTON_CREATE_PAYLOAD'
        );
        throw new Error('Request was aborted by client');
      }

      SyncLogger.logOperation(operation, 'BUTTON_CREATE_PAYLOAD_PREP', {
        userId,
      });

      const data = {
        ...operation.payload,
        userId,
        localButtonId: operation.localButtonId,
      };
      const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

      if (!buttonType) {
        const error = new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        SyncLogger.logError(operation, error, 'BUTTON_CREATE_VALIDATION', {
          userId,
          invalidButtonType: data.buttonType,
        });
        throw error;
      }

      const payload = {
        user_id: userId,
        button_uuid: data.buttonUuid,
        button_name: data.buttonName,
        button_color: data.buttonColor,
        button_type: buttonType,
        button_shape: data.buttonShape,
        button_summery_calculation: JSON.stringify(
          data.buttonSummeryCalculation
        ),
        button_sequence: data.buttonSequence,
        // Add metadata for tracking
        _localButtonId: operation.localButtonId,
        _originalIndex: operation.originalIndex,
        _operationType: operation.operationType,
      };

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.count_inc = Number(data.countInc);
        updateOnDuplicateFields.push('count_inc');
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.value_unit = data.valueUnit || null;
        payload.value_unit_description = data.valueUnitDescription || null;
        payload.value_item_name = data.valueItemName;
        updateOnDuplicateFields.push(
          'value_unit',
          'value_unit_description',
          'value_item_name'
        );
      }

      // Add optional tag fields
      if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
      if (data.textNoteTag !== undefined)
        payload.text_note_tag = data.textNoteTag;
      if (data.locationTag !== undefined)
        payload.location_tag = data.locationTag;

      bulkPayload.push(payload);

      SyncLogger.logOperation(operation, 'BUTTON_CREATE_PAYLOAD_READY', {
        userId,
        payloadSize: Object.keys(payload).length,
      });
    }

    SyncLogger.logBulkOperation(
      'BUTTON_CREATE',
      createOps,
      'PAYLOAD_PREPARATION_COMPLETE',
      {
        userId,
        totalPayloadItems: bulkPayload.length,
        updateOnDuplicateFields,
      }
    );

    try {
      SyncLogger.logBulkOperation(
        'BUTTON_CREATE',
        createOps,
        'DB_EXECUTION_START',
        {
          userId,
          bulkPayloadSize: bulkPayload.length,
          updateOnDuplicateFields,
        }
      );

      // Track database query
      this.#trackDbQuery('BUTTON_CREATE', 'BULK_CREATE', {
        operationCount: bulkPayload.length,
        queryType: 'createBulkButtons',
      });

      // Use bulk create with returning option to get IDs
      const createdButtons = await ButtonModel.createBulkButtons(bulkPayload, {
        transaction,
        returning: true,
        updateOnDuplicate: updateOnDuplicateFields,
      });

      this.#trackBulkOperation('BUTTON_CREATE', createdButtons.length, {
        userId,
        successfulCreations: createdButtons.length,
      });

      SyncLogger.logBulkOperation(
        'BUTTON_CREATE',
        createOps,
        'DB_EXECUTION_SUCCESS',
        {
          userId,
          createdCount: createdButtons.length,
        }
      );

      // Map created buttons to local IDs and prepare bulk alarm creation
      const alarmPayloads = [];
      const updateOnDuplicateAlarmFields = [
        'alarm_time',
        'snooze_time',
        'repeat_daily',
        'is_ring_after',
        'is_ring_after_always',
        'ring_after_time_stamp',
        'ring_after_time_ms',
        'is_active',
      ];

      for (let i = 0; i < createdButtons.length; i++) {
        const button = createdButtons[i];
        const originalOp = createOps[i];

        this.#buttonIdMap.set(originalOp.localButtonId, button.id);
        this.#alarmIdMap.set(button.id, button.id);
        this.#buttonCache.set(button.button_uuid, button);

        // Prepare alarm payload for bulk creation
        alarmPayloads.push({
          button_id: button.id,
          user_id: userId,
          button_uuid: button.button_uuid,
          alarm_time: null,
          snooze_time: null,
          repeat_daily: false,
          is_ring_after: false,
          is_ring_after_always: false,
          ring_after_time_stamp: null,
          ring_after_time_ms: null,
          is_active: false,
        });

        // Add to response data
        responseData.push({
          operationType: originalOp.operationType,
          syncId: originalOp.syncId,
          btnId: button.id,
          alarmId: button.id,
          itemId: null,
          tagId: null,
          localButtonId: originalOp.localButtonId,
          localItemId: null,
          localTagId: null,
          originalIndex: originalOp.originalIndex,
          buttonUuid: button.button_uuid,
          itemUuid: null,
          tagUuid: null,
        });

        SyncLogger.logOperation(originalOp, 'BUTTON_CREATE_MAPPED', {
          userId,
          buttonId: button.id,
          buttonUuid: button.button_uuid,
        });
      }

      // Bulk create alarms for all buttons
      if (alarmPayloads.length > 0) {
        SyncLogger.logBulkOperation('ALARM_CREATE', [], 'START', {
          userId,
          alarmCount: alarmPayloads.length,
        });

        this.#trackDbQuery('ALARM_CREATE', 'BULK_CREATE', {
          operationCount: alarmPayloads.length,
          queryType: 'createBulkAlarms',
        });

        await AlarmModel.createBulkAlarms(alarmPayloads, {
          transaction,
          updateOnDuplicate: updateOnDuplicateAlarmFields,
        });

        this.#trackBulkOperation('ALARM_CREATE', alarmPayloads.length, {
          userId,
          successfulCreations: alarmPayloads.length,
        });

        SyncLogger.logBulkOperation('ALARM_CREATE', [], 'COMPLETE', {
          userId,
          createdCount: alarmPayloads.length,
        });
      }

      SyncLogger.logBulkOperation('BUTTON_CREATE', createOps, 'COMPLETE', {
        userId,
        totalButtonsCreated: createdButtons.length,
        totalAlarmsCreated: alarmPayloads.length,
        totalResponsesAdded: createdButtons.length,
      });
    } catch (error) {
      SyncLogger.logError(
        { operationType: 'BUTTON_CREATE' },
        error,
        'BULK_CREATE_FAILED',
        {
          userId,
          operationCount: createOps.length,
          bulkPayloadSize: bulkPayload.length,
        }
      );

      // Track the error in performance metrics
      this.#performanceMetrics.errors.push({
        timestamp: Date.now(),
        stage: 'BUTTON_CREATE',
        error: error.message,
        operationCount: createOps.length,
      });

      throw error;
    }
  }

  // Helper to add button response data
  #addButtonResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    console.log(
      `[SyncController] Adding button response for operationType: ${operation.operationType}, syncId: ${operation.syncId}, btnId: ${buttonId}.`
    );
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: null,
      tagId: null,
      localButtonId: operation.localButtonId || null,
      localItemId: null,
      localTagId: null,
      buttonUuid: operation.buttonUuid || null,
      itemUuid: null,
      tagUuid: null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process item operations with optimized bulk creates
  async #processBulkItemOperations(
    itemOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    // Process creates first with bulk operation
    if (itemOps.create.length > 0) {
      await this.#bulkCreateItems(
        itemOps.create,
        userId,
        transaction,
        responseData,
        abortController,
        req
      );
    }

    // Process updates and deletes individually (need to check existing items)
    const individualOps = [...itemOps.update, ...itemOps.delete];

    if (individualOps.length > 0) {
      // Pre-fetch all required buttons to reduce N+1 queries
      await this.#preloadButtonsForItems(individualOps, userId, transaction);

      for (const operation of individualOps) {
        this.#checkRequestAborted(req);
        if (abortController.signal.aborted) {
          throw new Error('Request was aborted by client');
        }

        try {
          switch (operation.operationType) {
            case OPERATION_TYPES.EDIT_COUNT_BUTTON_ITEM:
            case OPERATION_TYPES.EDIT_DURATION_BUTTON_ITEM:
            case OPERATION_TYPES.EDIT_VALUE_BUTTON_ITEM:
              await this.updateItemSync(operation, userId, transaction);
              break;
            case OPERATION_TYPES.DELETE_COUNT_BUTTON_ITEM:
            case OPERATION_TYPES.DELETE_DURATION_BUTTON_ITEM:
            case OPERATION_TYPES.DELETE_VALUE_BUTTON_ITEM:
              await this.deleteItemSync(operation, userId, transaction);
              break;
          }
          this.#addItemResponse(operation, responseData);
        } catch (error) {
          // error.operationIndex = operation.originalIndex;
          // error.operationType = operation.operationType;
          error.operation = operation;
          throw error;
        }
      }
    }
  }

  // Bulk create items for better performance
  async #bulkCreateItems(
    createOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    if (createOps.length === 0) return;

    // Pre-validate all buttons exist and cache them
    const buttonUuids = new Set();
    createOps.forEach((op) => {
      const buttonUuid = op.buttonUuid;

      if (buttonUuid) buttonUuids.add(buttonUuid);
    });

    if (buttonUuids.size > 0) {
      const buttons = await ButtonModel.findButtons(
        { button_uuid: { [Op.in]: Array.from(buttonUuids) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries.total++;

      // Cache buttons using only button_uuid as key
      buttons.forEach((button) => {
        if (!this.#buttonCache.get(button.button_uuid)) {
          this.#buttonCache.set(button.button_uuid, button);
        }
      });
    }

    const bulkPayload = [];

    const updateOnDuplicateItemsFields = [
      'count_value',
      'count_time_stamp',
      'duration_start_time_stamp',
      'duration_stop_time_stamp',
      'duration_time_ms',
      'item_name',
      'item_value',
      'value_unit',
      'value_time_stamp',
      'display_time',
      'display_date',
      'display_month_year',
    ];

    for (const operation of createOps) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        throw new Error('Request was aborted by client');
      }

      const data = {
        ...operation.payload,
        userId,
        localItemId: operation.localItemId,
      };
      const buttonUuid = operation.buttonUuid;

      const button = this.#buttonCache.get(buttonUuid);

      if (!button) {
        const error = new Error(API.BUTTON_NOT_FOUND);
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }

      const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];
      if (!buttonType || buttonType !== button.button_type) {
        const error = new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }

      const payload = {
        button_id: button.id,
        user_id: userId,
        item_uuid: data.itemUuid,
        display_time: data.displayTime,
        display_date: data.displayDate,
        display_month_year: data.displayMonthYear,
        _localItemId: operation.localItemId,
        _originalIndex: operation.originalIndex,
      };

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.count_value = data.countIncrement;
        payload.count_time_stamp = data.countTimeStamp;
      } else if (buttonType === BUTTON_TYPES.DURATION) {
        payload.duration_start_time_stamp = data.durationStartTimeStamp;
        payload.duration_stop_time_stamp = data.durationStopTimeStamp;
        payload.duration_time_ms = data.durationTimeMs;
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.item_name = data.itemName;
        payload.item_value = data.itemValue;
        payload.value_unit = data.valueUnit;
        payload.value_time_stamp = data.valueTimeStamp;
      }

      bulkPayload.push(payload);
    }

    try {
      // Use Sequelize bulkCreate directly on the schema
      const { Item: ItemSchema } = require('../../Database/Schemas');
      const createdItems = await ItemSchema.bulkCreate(bulkPayload, {
        transaction,
        returning: true,
        validate: true,
        updateOnDuplicate: updateOnDuplicateItemsFields,
      });

      this.#performanceMetrics.bulkOperations.total++;
      this.#performanceMetrics.dbQueries.total++;

      // Map created items to local IDs and add responses
      for (let i = 0; i < createdItems.length; i++) {
        const item = Array.isArray(createdItems)
          ? createdItems[i]
          : createdItems;
        const originalOp = createOps[i];

        this.#itemIdMap.set(originalOp.localItemId, item.id);

        responseData.push({
          operationType: originalOp.operationType,
          syncId: originalOp.syncId,
          btnId: item.button_id,
          alarmId: this.#alarmIdMap.get(item.button_id) || null,
          itemId: item.id,
          tagId: null,
          localButtonId: originalOp.localButtonId,
          localItemId: originalOp.localItemId,
          localTagId: null,
          originalIndex: originalOp.originalIndex,
        });
      }
    } catch (error) {
      throw error;
      // Fallback to individual creates if bulk fails
      // for (const operation of createOps) {
      //   try {
      //     await this.createItemSync(operation, userId, transaction);
      //     this.#addItemResponse(operation, responseData);
      //   } catch (individualError) {
      //     individualError.operationIndex = operation.originalIndex;
      //     individualError.operationType = operation.operationType;
      //     throw individualError;
      //   }
      // }
    }
  }

  // Pre-load buttons to reduce N+1 queries
  async #preloadButtonsForItems(operations, userId, transaction) {
    const buttonUuids = new Set();
    operations.forEach((op) => {
      const buttonUuid = op.buttonUuid;

      if (buttonUuid) buttonUuids.add(buttonUuid);
    });

    if (buttonUuids.size > 0) {
      this.#trackDbQuery('ITEM_OPERATIONS', 'PRELOAD_BUTTONS', {
        buttonUuidCount: buttonUuids.size,
        queryType: 'findButtons',
      });

      const buttons = await ButtonModel.findButtons(
        { button_uuid: { [Op.in]: Array.from(buttonUuids) }, user_id: userId },
        false,
        { transaction }
      );

      // Cache buttons using only button_uuid as key
      buttons.forEach((button) => {
        this.#buttonCache.set(button.button_uuid, button);
      });

      SyncLogger.logBulkOperation('BUTTON_PRELOAD', [], 'COMPLETE', {
        userId,
        requestedCount: buttonUuids.size,
        foundCount: buttons.length,
        cachedButtons: buttons.map((b) => b.button_uuid),
      });
    }
  }

  // Helper to add item response data
  #addItemResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    const itemId = this.#itemIdMap.get(operation.localItemId) || null;
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: itemId,
      tagId: null,
      localButtonId: operation.localButtonId || null,
      localItemId: operation.localItemId || null,
      localTagId: null,
      buttonUuid: operation.buttonUuid || null,
      itemUuid: operation.itemUuid || null,
      tagUuid: null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process tag operations with optimized bulk creates
  async #processBulkTagOperations(
    tagOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    // Process creates first with bulk operation
    if (tagOps.create.length > 0) {
      await this.#bulkCreateTags(
        tagOps.create,
        userId,
        transaction,
        responseData,
        abortController,
        req
      );
    }

    // Process updates and deletes individually
    const individualOps = [...tagOps.update, ...tagOps.delete];

    if (individualOps.length > 0) {
      // Pre-fetch all required items to reduce N+1 queries
      await this.#preloadItemsForTags(individualOps, userId, transaction);

      for (const operation of individualOps) {
        this.#checkRequestAborted(req);
        if (abortController.signal.aborted) {
          throw new Error('Request was aborted by client');
        }

        try {
          switch (operation.operationType) {
            case OPERATION_TYPES.EDIT_ITEM_TAG:
              await this.updateTagSync(operation, userId, transaction);
              break;
            case OPERATION_TYPES.DELETE_ITEM_TAG:
              await this.deleteTagSync(operation, userId, transaction);
              break;
          }
          this.#addTagResponse(operation, responseData);
        } catch (error) {
          // error.operationIndex = operation.originalIndex;
          // error.operationType = operation.operationType;
          error.operation = operation;
          throw error;
        }
      }
    }
  }

  // Bulk create tags for better performance
  async #bulkCreateTags(
    createOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    if (createOps.length === 0) return;

    // Pre-validate all items exist and cache them using item_uuid
    const itemUuids = new Set();

    createOps.forEach((op) => {
      const itemUuid = op.itemUuid;
      // console.log(`[SyncController] Pre-validating item for tag create: ${op.itemUuid}`);

      if (itemUuid) itemUuids.add(itemUuid);
    });

    if (itemUuids.size > 0) {
      const items = await ItemModel.findItems(
        { item_uuid: { [Op.in]: Array.from(itemUuids) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries.total++;

      // Cache items using only item_uuid as key
      items.forEach((item) => {
        this.#itemCache.set(item.item_uuid, item);
      });
    }

    const bulkPayload = [];

    const updateOnDuplicateTagsFields = [
      'tag_type',
      'tag_title',
      'tag_value',
      'tag_time_stamp',
    ];

    for (const operation of createOps) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        throw new Error('Request was aborted by client');
      }

      const data = {
        ...operation.payload,
        userId,
        localTagId: operation.localTagId,
      };
      const itemUuid = operation.itemUuid;

      const item = this.#itemCache.get(itemUuid);

      if (!item) {
        const error = new Error(API.ITEM_NOT_FOUND);
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }

      const payload = {
        item_id: item.id,
        button_id: item.button_id,
        tag_uuid: data.tagUuid,
        tag_type: data.tagType,
        tag_title: data.tagTitle,
        tag_value: data.tagValue,
        tag_time_stamp: data.tagTimeStamp,
        _localTagId: operation.localTagId,
        _originalIndex: operation.originalIndex,
      };

      bulkPayload.push(payload);
    }

    try {
      // Use Sequelize bulkCreate directly on the schema
      const { Tag: TagSchema } = require('../../Database/Schemas');
      const createdTags = await TagSchema.bulkCreate(bulkPayload, {
        transaction,
        returning: true,
        validate: true,
        updateOnDuplicate: updateOnDuplicateTagsFields,
      });

      this.#performanceMetrics.bulkOperations.total++;
      this.#performanceMetrics.dbQueries.total++;

      // Map created tags to local IDs and add responses
      const tagsArray = Array.isArray(createdTags)
        ? createdTags
        : [createdTags];
      for (let i = 0; i < tagsArray.length; i++) {
        const tag = tagsArray[i];
        const originalOp = createOps[i];

        this.#tagIdMap.set(originalOp.localTagId, tag.id);

        responseData.push({
          operationType: originalOp.operationType,
          syncId: originalOp.syncId,
          btnId: tag.button_id,
          alarmId: this.#alarmIdMap.get(tag.button_id) || null,
          itemId: tag.item_id,
          tagId: tag.id,
          localButtonId: originalOp.localButtonId,
          localItemId: originalOp.localItemId,
          localTagId: originalOp.localTagId,
          buttonUuid: originalOp.buttonUuid,
          itemUuid: originalOp.itemUuid,
          tagUuid: tag.tag_uuid,
          originalIndex: originalOp.originalIndex,
        });
      }
    } catch (error) {
      throw error;
      // Fallback to individual creates if bulk fails
      // for (const operation of createOps) {
      //   try {
      //     await this.createTagSync(operation, userId, transaction);
      //     this.#addTagResponse(operation, responseData);
      //   } catch (individualError) {
      //     individualError.operationIndex = operation.originalIndex;
      //     individualError.operationType = operation.operationType;
      //     throw individualError;
      //   }
      // }
    }
  }

  // Pre-load items to reduce N+1 queries for tags using item_uuid
  async #preloadItemsForTags(operations, userId, transaction) {
    const itemUuids = new Set();
    operations.forEach((op) => {
      const itemUuid = op.payload.itemUuid;
      if (itemUuid) itemUuids.add(itemUuid);
    });

    if (itemUuids.size > 0) {
      const items = await ItemModel.findItems(
        { item_uuid: { [Op.in]: Array.from(itemUuids) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries.total++;
      this.#performanceMetrics.cacheHits.total += items.length;

      items.forEach((item) => {
        this.#itemCache.set(item.item_uuid, item);
      });
    }
  }

  // Helper to add tag response data
  #addTagResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    const itemId = this.#itemIdMap.get(operation.localItemId) || null;
    const tagId = this.#tagIdMap.get(operation.localTagId) || null;
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: itemId,
      tagId: tagId,
      localButtonId: operation.localButtonId || null,
      localItemId: operation.localItemId || null,
      localTagId: operation.localTagId || null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process alarm operations
  async #processBulkAlarmOperations(
    alarmOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    console.log('[SyncController] Processing alarm operations...');

    for (const operation of alarmOps.update) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn(
          '[SyncController] Request aborted during alarm operation.'
        );
        throw new Error('Request was aborted by client');
      }

      try {
        console.log(
          `[SyncController] Updating alarm sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}`
        );
        await this.updateAlarmSync(operation, userId, transaction);
        this.#addAlarmResponse(operation, responseData);
        console.log(
          `[SyncController] Successfully updated alarm sync for localButtonId: ${operation.localButtonId}.`
        );
      } catch (error) {
        console.error(
          `[SyncController] Error updating alarm sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}:`,
          error.message
        );
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }
    }
    console.log('[SyncController] Finished processing alarm operations.');
  }

  // Helper to add alarm response data
  #addAlarmResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    console.log(
      `[SyncController] Adding alarm response for operationType: ${operation.operationType}, syncId: ${operation.syncId}, buttonId: ${buttonId}.`
    );
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: null,
      tagId: null,
      localButtonId: operation.localButtonId || null,
      localItemId: null,
      localTagId: null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process sequence operations
  async #processBulkSequenceOperations(
    sequenceOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    console.log('[SyncController] Processing sequence operations...');
    for (const operation of sequenceOps.update) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn(
          '[SyncController] Request aborted during sequence operation.'
        );
        throw new Error('Request was aborted by client');
      }

      try {
        console.log(
          `[SyncController] Changing button sequence sync for syncId: ${operation.syncId}`
        );
        await this.changeButtonSequenceSync(operation, userId, transaction);
        this.#addSequenceResponse(operation, responseData);
        console.log(
          `[SyncController] Successfully changed button sequence sync for syncId: ${operation.syncId}.`
        );
      } catch (error) {
        console.error(
          `[SyncController] Error changing button sequence sync for syncId: ${operation.syncId}:`,
          error.message
        );
        // error.operationIndex = operation.originalIndex;
        // error.operationType = operation.operationType;
        error.operation = operation;
        throw error;
      }
    }
    console.log('[SyncController] Finished processing sequence operations.');
  }

  // Helper to add sequence response data
  #addSequenceResponse(operation, responseData) {
    console.log(
      `[SyncController] Adding sequence response for operationType: ${operation.operationType}, syncId: ${operation.syncId}.`
    );
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: null,
      alarmId: null,
      itemId: null,
      tagId: null,
      localButtonId: null,
      localItemId: null,
      localTagId: null,
      originalIndex: operation.originalIndex,
    });
  }

  async batchSync(req, res) {
    this.#resetMaps();
    this.#performanceMetrics.startTime = Date.now();

    const { operations } = req.body;
    const userId = req.userId;
    this.#performanceMetrics.totalOperations = operations.length;

    const { controller: abortController, cleanup } =
      this.#createAbortController(req);

    // Log batch sync start with complete request details
    SyncLogger.logBulkOperation('BATCH_SYNC', operations, 'START', {
      userId,
      totalOperations: operations.length,
      requestId: req.id || 'unknown',
      userAgent: req.get('User-Agent'),
      timestamp: this.#performanceMetrics.startTime,
    });

    // Check database connection before starting
    if (!(await this.#checkDatabaseConnection())) {
      const error = {
        message: 'Database connection not available. Please try again later.',
        code: 'DATABASE_UNAVAILABLE',
        retryable: true,
        timestamp: new Date().toISOString(),
      };

      SyncLogger.logError(
        { operationType: 'BATCH_SYNC' },
        new Error(error.message),
        'DB_CONNECTION_CHECK',
        {
          userId,
          operationCount: operations.length,
        }
      );

      cleanup();
      return res.handler.serverError(error);
    }

    // Use READ_COMMITTED isolation level for better performance
    const transaction = await ButtonModel.getTransaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });

    SyncLogger.logBulkOperation('BATCH_SYNC', [], 'TRANSACTION_STARTED', {
      userId,
      isolationLevel: 'READ_COMMITTED',
    });

    try {
      // Group operations by type for potential bulk processing
      const groupedOperations = this.#groupOperationsByType(operations);

      // Process operations in dependency order to maintain referential integrity
      const processedResults = await this.#processOperationsOptimized(
        groupedOperations,
        userId,
        transaction,
        abortController,
        req
      );

      await transaction.commit();

      SyncLogger.logBulkOperation('BATCH_SYNC', [], 'TRANSACTION_COMMITTED', {
        userId,
        processedResultsCount: processedResults.length,
      });

      this.#performanceMetrics.endTime = Date.now();
      const duration =
        this.#performanceMetrics.endTime - this.#performanceMetrics.startTime;

      // Enhanced performance summary with detailed metrics
      const performanceSummary = {
        totalOperations: this.#performanceMetrics.totalOperations,
        totalTime: duration,
        dbQueries: {
          total: this.#performanceMetrics.dbQueries?.total || 0,
          byOperation: this.#mapToObject(
            this.#performanceMetrics.dbQueries?.byOperation
          ),
          byStage: this.#mapToObject(
            this.#performanceMetrics.dbQueries?.byStage
          ),
          details: this.#performanceMetrics.dbQueries?.details || [],
        },
        bulkOperations: {
          total: this.#performanceMetrics.bulkOperations?.total || 0,
          byType: this.#mapToObject(
            this.#performanceMetrics.bulkOperations?.byType
          ),
          details: this.#performanceMetrics.bulkOperations?.details || [],
        },
        cacheHits: {
          total: this.#performanceMetrics.cacheHits?.total || 0,
          byType: this.#mapToObject(this.#performanceMetrics.cacheHits?.byType),
          details: this.#performanceMetrics.cacheHits?.details || [],
        },
        parallelOperations: this.#performanceMetrics.parallelOperations || 0,
        processingStages: this.#performanceMetrics.processingStages || {},
        averageTimePerOperation:
          this.#performanceMetrics.totalOperations > 0
            ? (duration / this.#performanceMetrics.totalOperations).toFixed(2)
            : '0.00',
        operationsPerSecond:
          duration > 0
            ? (
                (this.#performanceMetrics.totalOperations / duration) *
                1000
              ).toFixed(2)
            : '0.00',
        operationsByType: this.#mapToObject(
          this.#performanceMetrics.operationsByType
        ),
        errors: this.#performanceMetrics.errors || [],
        warnings: this.#performanceMetrics.warnings || [],
      };

      SyncLogger.logPerformance(performanceSummary, {
        userId,
        requestId: req.id || 'unknown',
        success: true,
      });

      SyncLogger.logBulkOperation('BATCH_SYNC', operations, 'SUCCESS', {
        userId,
        duration,
        processedResultsCount: processedResults.length,
        performanceSummary,
      });

      cleanup();
      return res.handler.success(null, processedResults);
    } catch (error) {
      SyncLogger.logError(
        { operationType: 'BATCH_SYNC' },
        error,
        'BATCH_SYNC_ERROR',
        {
          userId,
          operationCount: operations.length,
          duration: Date.now() - this.#performanceMetrics.startTime,
        }
      );

      // Handle database connection errors gracefully
      let rollbackError = null;
      try {
        await transaction.rollback();
        console.log(
          '[SyncController] Database transaction rolled back successfully.'
        );
      } catch (rollbackErr) {
        rollbackError = rollbackErr;
        console.error(
          '[SyncController] Transaction rollback failed:',
          rollbackErr
        );

        // If rollback fails due to connection loss, log it but don't crash
        if (
          rollbackErr.code === 'PROTOCOL_CONNECTION_LOST' ||
          rollbackErr.code === 'ER_SERVER_SHUTDOWN' ||
          rollbackErr.parent?.code === 'PROTOCOL_CONNECTION_LOST' ||
          rollbackErr.parent?.code === 'ER_SERVER_SHUTDOWN'
        ) {
          console.warn(
            '[SyncController] Database connection lost during rollback - transaction may be auto-rolled back by server'
          );
        }
      }

      cleanup();

      // Enhanced error response with context
      const errorResponse = {
        message: this.#getErrorMessage(error),
        code: this.#getErrorCode(error),
        // operationIndex: error.operationIndex || null,
        // operationType: error.operationType || null,
        operation: error.operation || null,
        timestamp: new Date().toISOString(),
        retryable: this.#isRetryableError(error),
        rollbackFailed: rollbackError !== null,
      };

      return res.handler.serverError(errorResponse);
    }
  }

  async updateButtonSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      buttonUuid: operation.buttonUuid,
    };

    // Check if either buttonId or localButtonId exists
    if (!data.buttonId && !data.localButtonId) {
      const error = new Error(
        'Either buttonId or localButtonId is required for button update'
      );
      SyncLogger.logError(operation, error, 'BUTTON_UPDATE_VALIDATION', {
        userId,
      });
      throw error;
    }

    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;
    if (!buttonId && !buttonUuid) {
      const error = new Error(API.BUTTON_NOT_FOUND);
      SyncLogger.logError(operation, error, 'BUTTON_UPDATE_ID_RESOLUTION', {
        userId,
        localButtonId: data.localButtonId,
      });
      throw error;
    }

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType) {
      const error = new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
      SyncLogger.logError(operation, error, 'BUTTON_UPDATE_TYPE_VALIDATION', {
        userId,
        buttonId,
        invalidButtonType: data.buttonType,
      });
      throw error;
    }

    // Get the existing button
    this.#trackDbQuery('BUTTON_UPDATE', 'FIND_BUTTON', {
      buttonId,
      buttonUuid,
      queryType: 'findButton',
    });

    const button = await ButtonModel.findButton(
      {
        [Op.or]: {
          button_uuid: buttonUuid,
          id: buttonId,
        },
        user_id: data.userId,
      },
      false,
      { transaction }
    );

    if (!button) {
      const error = new Error(API.BUTTON_NOT_FOUND);
      SyncLogger.logError(operation, error, 'BUTTON_UPDATE_NOT_FOUND', {
        userId,
        buttonId,
        buttonUuid,
      });
      throw error;
    }

    const payload = {
      button_name: data.buttonName,
      button_color: data.buttonColor,
      button_shape: data.buttonShape,
      button_summery_calculation: JSON.stringify(data.buttonSummeryCalculation),
      button_sequence: data.buttonSequence,
    };

    // Check if button type is changed
    if (buttonType !== undefined && buttonType !== button.button_type) {
      SyncLogger.logOperation(operation, 'BUTTON_TYPE_CHANGE_DETECTED', {
        userId,
        buttonId,
        oldType: button.button_type,
        newType: buttonType,
      });

      // Check if items exist for this button
      this.#trackDbQuery('BUTTON_UPDATE', 'COUNT_ITEMS', {
        buttonId,
        queryType: 'countItems',
      });

      const items = await ItemModel.countItems({ button_id: buttonId }, false, {
        transaction,
      });

      if (items > 0) {
        const error = new Error(API.BUTTON_HAVE_ITEM_HISTORY);
        SyncLogger.logError(operation, error, 'BUTTON_TYPE_CHANGE_BLOCKED', {
          userId,
          buttonId,
          itemCount: items,
        });
        throw error;
      }

      SyncLogger.logOperation(operation, 'BUTTON_TYPE_CHANGE_ALLOWED', {
        userId,
        buttonId,
        itemCount: items,
      });

      payload.button_type = buttonType;

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.value_unit = null;
        payload.value_unit_description = null;
        payload.value_item_name = null;
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.count_inc = null;
      } else if (buttonType === BUTTON_TYPES.DURATION) {
        payload.count_inc = null;
        payload.value_unit = null;
        payload.value_unit_description = null;
        payload.value_item_name = null;
      }
    }

    // Add type-specific fields based on the current button type
    if (button.button_type === BUTTON_TYPES.COUNT) {
      payload.count_inc = Number(data.countInc);
    } else if (button.button_type === BUTTON_TYPES.VALUE) {
      payload.value_unit = data.valueUnit || null;
      payload.value_unit_description = data.valueUnitDescription || null;
      payload.value_item_name = data.valueItemName;
    }

    // Add optional tag fields
    if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
    if (data.textNoteTag !== undefined)
      payload.text_note_tag = data.textNoteTag;
    if (data.locationTag !== undefined) payload.location_tag = data.locationTag;

    // Update the button
    this.#trackDbQuery('BUTTON_UPDATE', 'UPDATE_BUTTON', {
      buttonId,
      payloadKeys: Object.keys(payload),
      queryType: 'updateButton',
    });

    await ButtonModel.updateButton(
      { id: button.id, user_id: userId },
      payload,
      {
        transaction,
      }
    );

    this.#alarmIdMap?.set(button.id, button.id);
    this.#buttonIdMap?.set(operation.localButtonId, button.id);

    // Update the cache with the new button data if it exists
    if (this.#buttonCache.has(button.button_uuid)) {
      // const cachedButton = this.#buttonCache.get(button.button_uuid);
      this.#buttonCache.delete(button.button_uuid);
    }

    SyncLogger.logOperation(operation, 'BUTTON_UPDATE_COMPLETE', {
      userId,
      buttonId,
      updatedFields: Object.keys(payload),
    });
  }

  async deleteButtonSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      buttonUuid: operation.buttonUuid,
    };
    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;

    if (!buttonId && !buttonUuid) {
      const error = new Error(API.BUTTON_NOT_FOUND);
      SyncLogger.logError(operation, error, 'BUTTON_DELETE_ID_RESOLUTION', {
        userId,
        localButtonId: data.localButtonId,
      });
      throw error;
    }

    this.#trackDbQuery('BUTTON_DELETE', 'FIND_BUTTON', {
      buttonId,
      buttonUuid,
      queryType: 'findButton',
    });

    const button = await ButtonModel.findButton(
      {
        [Op.or]: {
          button_uuid: buttonUuid,
          id: buttonId,
        },
        user_id: userId,
      },
      false,
      { transaction }
    );

    if (!button) {
      const error = new Error(API.BUTTON_NOT_FOUND);
      SyncLogger.logError(operation, error, 'BUTTON_DELETE_NOT_FOUND', {
        userId,
        buttonId,
        buttonUuid,
      });
      throw error;
    }

    if (button.is_deleted) {
      const error = new Error(API.BUTTON_ALREADY_DELETED);
      SyncLogger.logError(operation, error, 'BUTTON_DELETE_ALREADY_DELETED', {
        userId,
        buttonId,
      });
      throw error;
    }

    this.#trackDbQuery('BUTTON_DELETE', 'UPDATE_BUTTON', {
      buttonId,
      queryType: 'updateButton',
      operation: 'mark_deleted',
    });

    await ButtonModel.updateButton(
      { id: button.id, user_id: userId },
      { is_deleted: true },
      { transaction }
    );

    this.#alarmIdMap?.set(button.id, button.id);
    this.#buttonIdMap?.set(operation.localButtonId, button.id);

    SyncLogger.logOperation(operation, 'BUTTON_DELETE_COMPLETE', {
      userId,
      buttonId,
      buttonUuid: button.button_uuid,
    });
  }

  async updateItemSync(operation, userId, transaction) {
    console.log(
      `[SyncController] Initiating updateItemSync for localItemId: ${operation.localItemId}, itemId: ${operation.itemId}, localButtonId: ${operation.localButtonId}.`
    );
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      buttonUuid: operation.buttonUuid,
      itemUuid: operation.itemUuid,
    };
    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId) || null;
    const itemUuid = data.itemUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Update item failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Update item failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    // Try to get button from cache first using button_uuid
    let button = this.#buttonCache.get(buttonUuid);
    if (!button) {
      this.#trackDbQuery('ITEM_UPDATE', 'FIND_BUTTON', {
        buttonId,
        buttonUuid,
        queryType: 'findButton',
      });

      button = await ButtonModel.findButton(
        {
          [Op.or]: {
            button_uuid: buttonUuid,
            id: buttonId,
          },
          user_id: userId,
        },
        false,
        { transaction }
      );

      // Cache the button using only button_uuid as key
      if (button) {
        this.#buttonCache.set(button.button_uuid, button);
      }
    } else {
      this.#trackCacheHit('button', {
        buttonUuid,
        operationType: 'ITEM_UPDATE',
      });
    }

    if (!button) {
      console.error(
        `[SyncController] Update item failed: Button with ID: ${buttonId} not found for user: ${userId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    console.log(`[SyncController] Button with ID: ${buttonId} found.`);

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType || buttonType !== button.button_type) {
      console.error(
        `[SyncController] Update item failed: Button type mismatch for button ID: ${buttonId}. Expected: ${button.button_type}, Received: ${buttonType}.`
      );
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    console.log(`[SyncController] Finding item with ID: ${itemId} for update.`);
    const item = await ItemModel.findItem(
      {
        [Op.or]: {
          item_uuid: itemUuid,
          id: itemId,
        },
        user_id: userId,
        button_id: buttonId,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;

    if (!item) {
      console.error(
        `[SyncController] Update item failed: Item with ID: ${itemId} not found for button ID: ${buttonId}, user ID: ${userId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    console.log(`[SyncController] Item with ID: ${itemId} found.`);

    const payload = {
      display_time: data.displayTime,
      display_date: data.displayDate,
      display_month_year: data.displayMonthYear,
    };

    if (buttonType === BUTTON_TYPES.COUNT) {
      payload.count_value = data.countIncrement;
      payload.count_time_stamp = data.countTimeStamp;
    } else if (buttonType === BUTTON_TYPES.DURATION) {
      payload.duration_start_time_stamp = data.durationStartTimeStamp;
      payload.duration_time_ms = data.durationTimeMs;
    } else if (buttonType === BUTTON_TYPES.VALUE) {
      payload.item_value = data.itemValue;
      payload.value_time_stamp = data.valueTimeStamp;
    }

    console.log(
      `[SyncController] Updating item in DB for ID: ${itemId}, buttonId: ${buttonId}.`
    );
    await ItemModel.updateItem(
      {
        id: item.id,
        user_id: userId,
        button_id: buttonId,
      },
      payload,
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;
    console.log(`[SyncController] Item ID: ${itemId} updated successfully.`);

    this.#itemIdMap?.set(operation.localItemId, item.id);
    this.#buttonIdMap?.set(operation.localButtonId, buttonId || item.button_id);

    // Update the cache with the new item data if it exists
    if (this.#itemCache.has(item.item_uuid)) {
      // const cachedItem = this.#itemCache.get(item.item_uuid);
      this.#itemCache.delete(item.item_uuid);
    }

    console.log(
      `[SyncController] updateItemSync completed for localItemId: ${operation.localItemId}, item ID: ${itemId}.`
    );
  }

  async deleteItemSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      buttonUuid: operation.buttonUuid,
      itemUuid: operation.itemUuid,
    };
    console.log(
      `[SyncController] Initiating deleteItemSync for localItemId: ${data.localItemId}, itemId: ${data.itemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId) || null;
    const itemUuid = data.itemUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Delete item failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Delete item failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    console.log(
      `[SyncController] Finding item with ID: ${itemId} for deletion.`
    );
    const item = await ItemModel.findItem(
      {
        [Op.or]: {
          item_uuid: itemUuid,
          id: itemId,
        },
        user_id: userId,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;

    if (!item) {
      console.error(
        `[SyncController] Delete item failed: Item with ID: ${itemId} not found for button ID: ${buttonId}, user ID: ${userId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    if (item.is_deleted) {
      console.warn(
        `[SyncController] Delete item failed: Item with ID: ${itemId} is already marked as deleted.`
      );
      throw new Error(API.ITEM_ALREADY_DELETED);
    }
    console.log(
      `[SyncController] Item with ID: ${itemId} found and is not deleted. Marking as deleted.`
    );

    await ItemModel.updateItem(
      {
        item_uuid: itemUuid,
        id: item.id,
        user_id: userId,
      },
      { is_deleted: true },
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;
    console.log(
      `[SyncController] Item ID: ${itemId} successfully marked as deleted.`
    );

    this.#itemIdMap?.set(operation.localItemId, item.id);
    this.#buttonIdMap?.set(operation.localButtonId, buttonId || item.button_id);
    console.log(
      `[SyncController] deleteItemSync completed for localItemId: ${data.localItemId}, item ID: ${itemId}.`
    );
  }

  async updateTagSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      localTagId: operation.localTagId,
      buttonUuid: operation.buttonUuid,
      itemUuid: operation.itemUuid,
      tagUuid: operation.tagUuid,
    };
    console.log(
      `[SyncController] Initiating updateTagSync for localTagId: ${data.localTagId}, tagId: ${data.tagId}, localItemId: ${data.localItemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId) || null;
    const itemUuid = data.itemUuid;
    const tagId = data.tagId || this.#tagIdMap.get(data.localTagId) || null;
    const tagUuid = data.tagUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Update tag failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Update tag failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    if (!tagId && !tagUuid) {
      console.error(
        `[SyncController] Update tag failed: Tag not found for localTagId: ${data.localTagId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }

    // Verify item exists
    console.log(
      `[SyncController] Verifying item exists for itemId: ${itemId}, buttonId: ${buttonId}.`
    );

    let item = this.#itemCache.get(itemUuid);

    if (!item) {
      item = await ItemModel.findItem(
        {
          [Op.or]: {
            item_uuid: itemUuid,
            id: itemId,
          },
          button_id: buttonId,
        },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries.total++;

      this.#itemCache.set(itemUuid, item);
    }

    if (!item) {
      console.error(
        `[SyncController] Update tag failed: Item with ID: ${itemId} not found for button ID: ${buttonId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    console.log(`[SyncController] Item with ID: ${itemId} found.`);

    // Find and verify tag
    console.log(`[SyncController] Finding tag with ID: ${tagId} for update.`);

    let tag = this.#tagCache.get(tagUuid);

    if (!tag) {
      tag = await TagModel.findTag(
        {
          [Op.or]: {
            tag_uuid: tagUuid,
            id: tagId,
          },
          item_id: item.id,
          button_id: item.button_id,
        },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries.total++;

      this.#tagCache.set(tagUuid, tag);
    }

    if (!tag) {
      console.error(
        `[SyncController] Update tag failed: Tag with ID: ${tagId} not found for button ID: ${buttonId}, item ID: ${itemId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }
    console.log(`[SyncController] Tag with ID: ${tagId} found.`);

    // Check if tag type matches
    if (tag.tag_type !== data.tagType) {
      console.error(
        `[SyncController] Update tag failed: Tag type mismatch for tag ID: ${tagId}. Expected: ${tag.tag_type}, Received: ${data.tagType}.`
      );
      throw new Error(API.TAG_TYPE_DO_NOT_MATCH);
    }

    // Check if it's a GPS tag (which can't be updated)
    if (tag.tag_type === TAG_TYPES.GPS) {
      console.error(
        `[SyncController] Update tag failed: Cannot update GPS tag with ID: ${tagId}.`
      );
      throw new Error(API.CANNOT_UPDATE_GPS_TAG);
    }

    const payload = {
      tag_title: data.tagTitle,
      tag_value: data.tagValue,
      tag_time_stamp: data.tagTimeStamp,
    };

    console.log(`[SyncController] Updating tag in DB for ID: ${tagId}.`);
    await TagModel.updateTag({ id: tag.id, tag_uuid: tagUuid }, payload, {
      transaction,
    });
    this.#performanceMetrics.dbQueries.total++;
    console.log(`[SyncController] Tag ID: ${tagId} updated successfully.`);

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId || item.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId || item.button_id);

    // Update the cache with the new tag data if it exists
    if (this.#tagCache.has(tag.tag_uuid)) {
      // const cachedTag = this.#tagCache.get(tag.tag_uuid);
      this.#tagCache.delete(tag.tag_uuid);
    }

    console.log(
      `[SyncController] updateTagSync completed for localTagId: ${data.localTagId}, tag ID: ${tagId}.`
    );
  }

  async deleteTagSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      localTagId: operation.localTagId,
      buttonUuid: operation.buttonUuid,
      itemUuid: operation.itemUuid,
      tagUuid: operation.tagUuid,
    };
    console.log(
      `[SyncController] Initiating deleteTagSync for localTagId: ${data.localTagId}, tagId: ${data.tagId}, localItemId: ${data.localItemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId) || null;
    const itemUuid = data.itemUuid;
    const tagId = data.tagId || this.#tagIdMap.get(data.localTagId) || null;
    const tagUuid = data.tagUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Delete tag failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Delete tag failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    if (!tagId && !tagUuid) {
      console.error(
        `[SyncController] Delete tag failed: Tag not found for localTagId: ${data.localTagId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }

    // Find tag to verify it exists
    console.log(`[SyncController] Finding tag with ID: ${tagId} for deletion.`);
    const tag = await TagModel.findTag(
      {
        [Op.or]: {
          tag_uuid: tagUuid,
          id: tagId,
        },
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;

    if (!tag) {
      console.error(
        `[SyncController] Delete tag failed: Tag with ID: ${tagId} not found for button ID: ${buttonId}, item ID: ${itemId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }

    if (tag.is_deleted) {
      console.warn(
        `[SyncController] Delete tag failed: Tag with ID: ${tagId} is already marked as deleted.`
      );
      throw new Error(API.TAG_ALREADY_DELETED);
    }
    console.log(
      `[SyncController] Tag with ID: ${tagId} found and is not deleted. Marking as deleted.`
    );

    // Mark tag as deleted
    await TagModel.updateTag(
      { id: tagId || tag.id, tag_uuid: tagUuid },
      { is_deleted: true },
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;
    console.log(
      `[SyncController] Tag ID: ${tagId} successfully marked as deleted.`
    );

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId || tag.item_id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId || tag.button_id);
    console.log(
      `[SyncController] deleteTagSync completed for localTagId: ${data.localTagId}, tag ID: ${tagId}.`
    );
  }

  async updateAlarmSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      buttonUuid: operation.buttonUuid,
    };
    console.log(
      `[SyncController] Initiating updateAlarmSync for localButtonId: ${data.localButtonId}, buttonId: ${data.buttonId}, alarmId: ${data.alarmId}.`
    );
    const buttonId =
      data.buttonId || this.#buttonIdMap.get(data.localButtonId) || null;
    const buttonUuid = data.buttonUuid;
    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Update alarm failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    const alarmId = data.alarmId || this.#alarmIdMap.get(buttonId) || null;
    if (!alarmId && !buttonUuid) {
      console.error(
        `[SyncController] Update alarm failed: Alarm not found for buttonId: ${buttonId}.`
      );
      throw new Error(API.ALARM_NOT_FOUND);
    }

    console.log(
      `[SyncController] Finding alarm with ID: ${alarmId} for update.`
    );
    const alarm = await AlarmModel.findAlarm(
      {
        [Op.or]: {
          button_uuid: buttonUuid,
          id: alarmId,
        },
        user_id: userId,
      },
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;

    if (!alarm) {
      console.error(
        `[SyncController] Update alarm failed: Alarm with ID: ${alarmId} not found for button ID: ${buttonId}.`
      );
      throw new Error(API.ALARM_NOT_FOUND);
    }
    console.log(`[SyncController] Alarm with ID: ${alarmId} found.`);

    const payload = {
      alarm_time: data.alarmTime,
      snooze_time: data.snoozeTime,
      repeat_daily: data.repeatDaily,
      is_ring_after: data.isRingAfter,
      is_ring_after_always: data.isRingAfterAlways,
      ring_after_time_stamp: data.ringAfterTimeStamp,
      ring_after_time_ms: data.ringAfterTimeMs,
      is_active: data.isActive,
    };

    console.log(
      `[SyncController] Updating alarm in DB for ID: ${alarmId}, buttonId: ${buttonId}.`
    );
    await AlarmModel.updateAlarm(
      { id: alarm.id, button_id: alarm.button_id, user_id: data.userId },
      payload,
      { transaction }
    );
    this.#performanceMetrics.dbQueries.total++;
    console.log(`[SyncController] Alarm ID: ${alarmId} updated successfully.`);

    this.#alarmIdMap?.set(buttonId, alarm.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
    console.log(
      `[SyncController] updateAlarmSync completed for button ID: ${buttonId}, alarm ID: ${alarmId}.`
    );
  }

  async changeButtonSequenceSync(operation, userId, transaction) {
    const data = { ...operation.payload, userId };
    console.log(
      `[SyncController] Initiating changeButtonSequenceSync for user ID: ${data.userId}.`
    );
    try {
      const { buttonSequence } = data;

      const buttonUuids = buttonSequence.map((sequence) => {
        // const buttonId =
        //   sequence.buttonId || this.#buttonIdMap.get(sequence.localButtonId);
        const buttonUuid = sequence.buttonUuid;

        if (!buttonUuid) {
          console.error(
            `[SyncController] Change button sequence failed: Button not found for localButtonId: ${sequence.localButtonId}.`
          );
          throw new Error(API.BUTTON_NOT_FOUND);
        }
        return buttonUuid;
      });
      console.log(
        `[SyncController] Extracted button UUIDs for sequence change: ${buttonUuids.join(
          ', '
        )}.`
      );

      console.log(
        `[SyncController] Counting buttons for user ID: ${userId} with provided IDs.`
      );
      const buttonsCount = await ButtonModel.countButtons(
        {
          user_id: userId,
          button_uuid: {
            [Op.in]: buttonUuids,
          },
        },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries.total++;

      if (!buttonsCount || buttonsCount !== buttonSequence.length) {
        console.error(
          `[SyncController] Change button sequence failed: Mismatch in button count. Found: ${buttonsCount}, Expected: ${buttonSequence.length}.`
        );
        throw new Error(API.BUTTON_NOT_FOUND);
      }
      console.log(`[SyncController] Button count matched: ${buttonsCount}.`);

      const sequences = buttonSequence.map((b) => b.sequence);
      if (new Set(sequences).size !== sequences.length) {
        console.error(
          `[SyncController] Change button sequence failed: Duplicate sequence found in payload.`
        );
        throw new Error(API.DUPLICATE_SEQUENCE_FOUND);
      }
      console.log(
        `[SyncController] No duplicate sequences found. Proceeding with updates.`
      );

      const updatePromises = buttonSequence.map((sequence) => {
        const buttonUuid = sequence.buttonUuid;
        console.log(
          `[SyncController] Preparing update for button UUID: ${buttonUuid} to sequence: ${sequence.sequence}.`
        );
        return ButtonModel.updateButton(
          {
            button_uuid: buttonUuid,
            user_id: userId,
          },
          { button_sequence: sequence.sequence },
          { transaction }
        );
      });

      console.log(
        `[SyncController] Executing all button sequence update promises.`
      );
      await Promise.all(updatePromises);
      this.#performanceMetrics.dbQueries.total += updatePromises.length;
      console.log(
        `[SyncController] All button sequence updates completed successfully.`
      );
    } catch (error) {
      console.error(
        `[SyncController] Error in changeButtonSequenceSync for user ID: ${data.userId}:`,
        error.message
      );
      throw error;
    }
    console.log(
      `[SyncController] changeButtonSequenceSync completed for user ID: ${data.userId}.`
    );
  }
}

module.exports = SyncController;
