const AuthModal = new (require('../../Models/App/AuthModel'))();
const { USER_TYPE, STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');
const Bcryptjs = require('bcryptjs'); /* For encryption and decryption */
const { getSubscriptionDetails } = require('../../Helpers/gcp.helper');
const generatedSalt = Bcryptjs.genSaltSync(10);

class UserController {
  async getUserProfile(req, res) {
    try {
      const userId = req.userId;

      const user = await AuthModal.findUser({
        id: userId,
        is_active: true,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      const userProfile = AuthModal.userFormatInstance(user);

      return res.handler.success(API.USER_PROFILE, userProfile);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async updateUserProfile(req, res) {
    try {
      const userId = req.userId;
      const body = req.body;

      const user = await AuthModal.findUser({
        id: userId,
        is_active: true,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      const payload = {
        name: body.name,
      };

      await AuthModal.userUpdate(payload, { id: userId });

      const updatedUser = await AuthModal.findUser({
        id: userId,
      });

      const userProfile = AuthModal.userFormatInstance(updatedUser);

      return res.handler.success(API.UPDATE_PROFILE_SUCCESS, userProfile);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async deleteUserProfile(req, res) {
    try {
      const userId = req.userId;

      const user = await AuthModal.findUser({
        id: userId,
        is_active: true,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      await AuthModal.userUpdate({ is_deleted: true }, { id: userId });

      return res.handler.success(API.USER_DELETE_SUCCESS);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async changePassword(req, res) {
    try {
      const body = req.body;
      const userId = req.userId;

      const user = await AuthModal.findUser({
        id: userId,
        is_active: true,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      if (user.user_type !== USER_TYPE.NORMAL) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.USER_NORMAL_PASSWORD_CHANGE
        );
      }

      if (!user.is_verified) {
        return res.handler.custom(STATUS_CODES.CONFLICT, API.USER_NOT_VERIFIED);
      }

      const passwordMatch = await Bcryptjs.compare(
        body.currentPassword,
        user.password
      );

      if (!passwordMatch) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.CHANGE_PASSWORD_FAILED
        );
      }

      const newPassword = await Bcryptjs.hash(body.newPassword, generatedSalt);

      await AuthModal.userUpdate({ password: newPassword }, { id: user.id });

      return res.handler.success(API.CHANGE_PASSWORD_SUCCESS);
    } catch (err) {
      return res.handler.serverError(err);
    }
  }

  async upgradeStatus(req, res) {
    try {
      const body = req.body;
      const userId = req.userId;

      const user = await AuthModal.findUser({
        id: userId,
        is_active: true,
      });

      if (!user) {
        return res.handler.notFound(API.USER_NOT_FOUND);
      }

      await AuthModal.userUpdate(
        {
          is_upgraded: true,
          price: body.price,
          purchase_date: new Date(Date.now()).toISOString(),
        },
        { id: userId }
      );

      const userData = await AuthModal.findUser({
        id: userId,
      });

      const data = {
        price: userData.price,
        purchaseDate: userData.purchase_date,
      };

      return res.handler.success(null, data);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async checkToken(req, res) {
    try {
      const { token } = req.body;

      const tokens = await AuthModal.checkToken({
        token,
      });

      if (tokens.count > 0) {
        return res.handler.custom(STATUS_CODES.SUCCESS, API.TOKEN_VALID);
      } else {
        return res.handler.custom(STATUS_CODES.SUCCESS, API.INVALID_TOKEN);
      }
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async checkSub(req, res) {
    const { purchaseToken } = req.body;

    const subscriptionDetails = await getSubscriptionDetails(purchaseToken);

    if (!subscriptionDetails) {
      return res.handler.success(null, {
        isActive: false,
        isCanceled: false,
        isExpired: true,
        expirationDate: null,
        renewalDate: null,
      });
    }

    const subState = subscriptionDetails.subscriptionState;
    const lineItem = subscriptionDetails.lineItems?.[0]; // Get the first item

    let isActive = false;
    let isCanceled = false;
    let isExpired = false;
    let expirationDate = null;
    let renewalDate = null;

    if (lineItem?.expiryTime) {
      const expiry = new Date(lineItem.expiryTime);
      expirationDate = expiry.toUTCString();
    }

    console.log('subscription details:');
    console.dir(subscriptionDetails, { depth: 100 });

    const isAutoRenew = lineItem?.autoRenewingPlan?.autoRenewEnabled === true;

    switch (subState) {
      case 'SUBSCRIPTION_STATE_ACTIVE':
      case 'SUBSCRIPTION_STATE_IN_GRACE_PERIOD':
      case 'SUBSCRIPTION_STATE_PAUSED':
      case 'SUBSCRIPTION_STATE_ON_HOLD':
        isActive = true;
        if (isAutoRenew && expirationDate) {
          renewalDate = expirationDate;
          expirationDate = null;
        }
        break;

      case 'SUBSCRIPTION_STATE_CANCELED':
        isCanceled = true;
        isActive = true;
        break;

      case 'SUBSCRIPTION_STATE_EXPIRED':
      case 'SUBSCRIPTION_STATE_PENDING_PURCHASE_CANCELED':
        isExpired = true;
        isCanceled = true;
        break;

      default:
        // Pending, unspecified, etc.
        break;
    }

    return res.handler.success(null, {
      isActive,
      isCanceled,
      isExpired,
      expirationDate,
      renewalDate,
    });
  }
}

module.exports = UserController;
